import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CashModalComponent } from 'src/app/payment/cash-modal/cash-modal.component';
import { EftposModalComponent } from 'src/app/payment/eftpos-modal/eftpos-modal.component';
import { Payment, PaymentType, Transaction, financialRound } from 'src/app/payment/payment.service';
import { PaymentModalButton } from 'src/app/payment/payment-modal-button/payment-modal-button.component';
import Swal from 'sweetalert2';
import { first, map, take, tap, takeUntil } from 'rxjs/operators';
import * as staffActions from 'src/app/reducers/staff/staff.actions';
import { Router } from '@angular/router';
import { OpenCashDrawerAction } from 'src/app/printing/printing-definitions';
import { CartItem, cartItemToTranslog } from 'src/app/reducers/sales/cart/cart.reducer';
import * as cartSelectors from 'src/app/reducers/sales/cart/cart.selectors';
import * as receiptActions from 'src/app/reducers/receipt-printing/receipt.actions';
import { EftposClient, GetReceiptDto, TransactionDto, TranslogDto, TranspayDto, CustomerClubDto } from '../../../pos-server.generated';
import * as transActions from '../../../reducers/transaction/transaction.actions';
import * as customerClubSearchSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors';
import { BehaviorSubject, Observable, pipe, Subject } from 'rxjs';
import * as SysConfigSelectors from 'src/app/reducers/sys-config/sys-config.selectors';
import { CtransDto } from '../../../pos-server.generated';
import * as transSelectors from '../../../reducers/transaction/transaction.selectors';
import * as paymentActions from '../../../reducers/sales/payment/payment.actions';
import { Store, select } from '@ngrx/store';
import { AppState } from 'src/app/reducers';
import * as orderActions from '../../../reducers/order-item/order.actions'; // Import order actions
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';
import * as orderSelectors from '../../../reducers/order-item/order.selectors'; // Import order selectors
import * as sysSelectors from '../../../reducers/sys-config/sys-config.selectors';
import { CreateOrderDto, CustOrderHeaderDTO, CustOrderLineDTO, ReceiptTransactionDto, CreateOrderRequest } from '../../../pos-server.generated'; 
import { WaitingForEftposModalComponent } from '../../../payment/waiting-for-eftpos-modal/waiting-for-eftpos-modal.component';
import { EftposService, mapCartToLinklyBasket } from '../../../eftpos/eftpos.service';
import { ReceiptBatch, TextAction, CutAction, CutType, BarcodeAction, FeedAction } from '../../../printing/printing-definitions';
import { PrintingService, SolemateReceiptOptions } from 'src/app/printing/printing.service';
import * as sysConfigSelectors from 'src/app/reducers/sys-config/sys-config.selectors';
import * as customerClubUpdateActions from 'src/app/reducers/customer-club/customer-update/customer-update.actions'; // Added import
import { PointsUpdatePayload } from 'src/app/reducers/customer-club/customer-update/customer-update.actions'; // Added import
import { selectOrderNo } from '../../../reducers/order-item/order.selectors';

@Component({
  selector: 'pos-deposit-modal',
  templateUrl: './deposit-modal.component.html',
  styleUrls: ['./deposit-modal.component.scss']
})
export class DepositModalComponent implements OnInit, OnDestroy {

  @Input() depositAmount: number; // Input property to receive the deposit amount from the parent component
	@Input() amountDue: number;


	private amountPaidSubject = new BehaviorSubject<number>(0);
	amountPaid$ = this.amountPaidSubject.asObservable();
	depositRemaining$: Observable<number>;
	totalRemaining$: Observable<number>;
	private destroy$ = new Subject<void>();

	modalButtons: PaymentModalButton[]; // Store payment buttons for Cash and EFTPOS
  amountPaid: number = 0; // Total amount paid so far
  transaction: Transaction; // Transaction to handle payments

	sysStatus: any;
	public sysStatus$: Observable<any>;


  private selectedCustomerClubMember$: Observable<CustomerClubDto>;
  public selectedCustomerClubMember: CustomerClubDto = null;

  // Properties to store orderHeader and orderLines
  orderHeader: CustOrderHeaderDTO;
  orderLines: CustOrderLineDTO[];

  public cart$: Observable<CartItem[]>;
  transNo: number;
  cart: CartItem[] = [];
  orderNo: string;

	transNo$: Observable<number>;
	alwaysOpenCashTill: string = 'F';
	intEftReceipts: GetReceiptDto[];
	orderNo$: Observable<string>;

	private transactionSubject = new BehaviorSubject<Transaction>(new Transaction(0));

	transaction$ = this.transactionSubject.asObservable();
  private transactionDto: TransactionDto;
  cTrans$: Observable<CtransDto[]>;
  cTrans: CtransDto[];

  // Add new property
  softCreditLimit: string = 'F';

  // Added properties for points processing
  PointsPerDollar: number = 0;
  newCustomerPointsTotal: number = null;
  pointsEarned: number = 0;

  constructor(
    public activeModal: NgbActiveModal,
    private modalService: NgbModal,
    private store: Store<AppState>,
	  private router: Router,
	  private eftposService: EftposService,
	  private printService: PrintingService
  ) {
    this.transaction = new Transaction(0);
  }

  ngOnInit() {
    // Define payment buttons for Cash and EFTPOS
    this.modalButtons = [
      new PaymentModalButton('Cash', 'fas fa-money-bill-wave', PaymentType.Cash, false),
      new PaymentModalButton('EFTPOS', 'fas fa-credit-card', PaymentType.Eftpos, false)
	  ];
		this.store.dispatch(transActions.getTransactionNo());

		this.store.select(transSelectors.transNo)
		.pipe(
			takeUntil(this.destroy$),
			tap(transNo => {
			this.transNo = transNo;
			console.log('Transaction number updated:', transNo);
			})
		)
		.subscribe();

	  this.depositRemaining$ = this.amountPaid$.pipe(
		  takeUntil(this.destroy$),
		  map(amountPaid => Math.max(this.depositAmount - amountPaid, 0))
	  );

	  this.totalRemaining$ = this.amountPaid$.pipe(
		  takeUntil(this.destroy$),
		  map(amountPaid => Math.max(this.amountDue - amountPaid, 0))
	  );

	  this.sysStatus$ = this.store.select(sysSelectors.selectSysConfig);
	  this.sysStatus$.subscribe((sysconfig) => {
		  this.sysStatus = sysconfig
	  }
	  );

	  this.store.dispatch(orderActions.getOrderNo());
	  this.orderNo$ = this.store.select(selectOrderNo);
	  this.orderNo$.pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (orderNo) => {
        if (orderNo) {
          this.orderNo = orderNo;
          console.log('Order Number received:', this.orderNo);
        } else {
          console.warn('No order number received');
        }
      },
      error: (error) => {
        console.error('Error getting order number:', error);
      }
    });
    // Initialize transaction with the required deposit amount
    this.transaction = new Transaction(this.amountDue);

    // Retrieve orderHeader and orderLines from the store
    this.store.select(orderSelectors.selectOrderHeader).subscribe((header) => {
      this.orderHeader = header;
      console.log('Order Header:', this.orderHeader); // Added console log
    });

    this.store.select(orderSelectors.selectOrderLines).subscribe((lines) => {
      this.orderLines = lines;
      console.log('Order Lines:', this.orderLines); // Added console log
    });

    this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
    this.selectedCustomerClubMember$.subscribe((s) => { 
      this.selectedCustomerClubMember = s; 
      console.log(this.selectedCustomerClubMember); 
    });

    this.cart$ = this.store.select(cartSelectors.cart);
    this.cart$.subscribe(
      (s) => this.cart = s
    );

    // Add subscription to softCreditLimit
    this.store.select(sysConfigSelectors.selectSoftCreditLimit)
      .subscribe(limit => {
        this.softCreditLimit = limit || 'F';
      });

    // Subscribe to PointsPerDollar config
    this.store.select(sysSelectors.PointsPerDollar)
      .pipe(takeUntil(this.destroy$))
      .subscribe(limit => {
        this.PointsPerDollar = limit || 0;
      });
  }

  // Method to launch payment modal when button is clicked
  launchPaymentModal(type: PaymentType) {
    let modalRef;
    switch (type) {
      case PaymentType.Cash:
        modalRef = this.modalService.open(CashModalComponent, { size: 'xl', centered: true });
        break;
      case PaymentType.Eftpos:
			this.store.select(sysSelectors.selectSysConfig).pipe(
        first(), // Get the first emitted value and complete
        map(sysConfig => sysConfig && sysConfig.integratedEFTProvider !== 'None' ? sysConfig.integratedEFTProvider : null)
    ).subscribe(integrated => {
		if (integrated && integrated != 'None') {
			modalRef = this.modalService.open(EftposModalComponent, { size: 'xl', centered: true });
			modalRef.componentInstance.intEft = true;
		}
		else {
			modalRef = this.modalService.open(EftposModalComponent, { size: 'xl', centered: true });
			modalRef.componentInstance.intEft = false;
		}
	});
        break;
    }

    if (modalRef) {
      modalRef.componentInstance.amountDue = this.getTotalAmountRemaining(); // Pass the remaining deposit amount to payment modal
      modalRef.result.then((result: Payment) => {
        if (result) {
          console.log('Payment Result:', result);
          this.handlePayment(result); // Handle payment logic here without closing the deposit modal
        }
	  }).catch((error) => {
        console.log('Payment Cancelled...');
      });
    }
  }

	getSaleTransaction(): TransactionDto {
	  // Get all translogs
	  let resTranslogs: TranslogDto[] = [];
	  
	  for (let i = 0; i < this.cart.length; i++) {
		let cartItem = this.cart[i];
	
		// Ensure the cart item and its essential properties are valid
		if (!cartItem || !cartItem.stockItem || cartItem.quantity == null || cartItem.quantity <= 0) {
		  console.warn(`Skipping invalid cart item at index ${i}:`, cartItem);
		  continue;
		}
	
		let lineNumber = i + 1;
	
		// Check if the customer club member exists and has a clientCode
		let clientCode = (this.selectedCustomerClubMember && this.selectedCustomerClubMember.clientCode)
		  ? this.selectedCustomerClubMember.clientCode
		  : undefined;
	
		resTranslogs.push(cartItemToTranslog(cartItem, lineNumber, this.transNo, clientCode));
	  }
	
	  // Create the base transaction object
	  let transaction: TransactionDto = {
		payments: this.transaction.toTranspayDtos(),
		translogs: resTranslogs,
		transType: 11
	  };
	
	  console.log("The transaction", transaction);
	  return transaction;
	}	

	finalizeTransaction(): void {
		this.store.dispatch(transActions.init());
		this.store.dispatch(paymentActions.init());
		// Init the transaction reducer and navigate to home
	}

  // Method to process the payment and update the transaction
  handlePayment(payment: Payment) {
    console.log('Deposit Payment Processed:', payment);
    let success = this.transaction.addPayment(payment);

	  if (success) {
		  const currentPaid = this.amountPaidSubject.getValue();
		  const newPaid = currentPaid + payment.amount;
		  this.amountPaidSubject.next(newPaid);

      this.amountPaid += payment.amount; // Update the total amount paid
      console.log('Total Amount Paid:', this.amountPaid); // Added console log
    } else {
      // Show warning if payment exceeds the amount due
      Swal.fire({
        title: "Error",
        text: "The payment exceeds the remaining deposit amount.",
      });
    }
  }

  // Method to calculate the amount remaining
  getDepositAmountRemaining(): number {
    return Math.max(this.depositAmount - this.amountPaid, 0);
	}

	getTotalAmountRemaining(): number {
		return Math.max(this.amountDue - this.amountPaid, 0);
	}

  // Method to dismiss the deposit modal
  dismissModal() {
    this.activeModal.dismiss('Modal closed by user');
  }

  // Method to check if the deposit is fully paid
  isDepositFullyPaid(): boolean {
    return this.amountPaid >= this.depositAmount;
  }

  openEmailModal(receiptTrans: ReceiptTransactionDto, pointsEarned: number, newPointsTotal: number | null): Promise<void> {
		return new Promise((resolve) => {
		  const modalRef = this.modalService.open(EmailReceiptComponent, {
			size: 'lg',
			backdrop: 'static'
		  });
	  
		  // Pass receiptTrans to the EmailReceiptComponent
		  modalRef.componentInstance.receiptTrans = receiptTrans;
          modalRef.componentInstance.customerSelected = this.selectedCustomerClubMember; // Pass customer object
          modalRef.componentInstance.pointsEarned = pointsEarned; // Pass points earned
          modalRef.componentInstance.newCustomerPointsTotal = newPointsTotal; // Pass new total points
		  modalRef.componentInstance.orderNo = this.orderNo;

		  // Check if a customer club member is selected and pass the email
		  if (this.selectedCustomerClubMember != null && this.selectedCustomerClubMember.email) {
			modalRef.componentInstance.customerEmail = this.selectedCustomerClubMember.email;
		  }
	  
		  modalRef.result.then(() => {
			console.log('Email receipt sent.');
			resolve();  // Resolve the promise once the modal is closed
		  }).catch(() => {
			resolve();  // Resolve the promise if the modal is dismissed
		  });
		});
	  }

    // Method to process the entire transaction once the deposit is fully paid
    processTransaction() {
      if (!this.isDepositFullyPaid()) {
          if (this.softCreditLimit === 'T') {
              // Show warning but allow continuation
              Swal.fire({
                  title: "Warning",
                  text: `Required deposit is $${this.depositAmount.toFixed(2)}, but only $${this.amountPaid.toFixed(2)} has been paid. Do you want to continue anyway?`,
                  showCancelButton: true,
                  confirmButtonText: "Continue",
                  cancelButtonText: "Cancel"
              }).then((result) => {
                  if (result.value) {
                      this.proceedWithTransaction();
                  }
              });
          } else {
              // Show error and prevent continuation
              Swal.fire({
                  title: "Error",
                  text: "Please complete the required deposit payment.",
              });
              return;
          }
      } else {
          this.proceedWithTransaction();
      }
  }

  // Add new method to handle the transaction processing
  private proceedWithTransaction() {
      if (!this.orderHeader || !this.orderLines) {
          Swal.fire({
              title: "Error",
              text: "Order details are missing.",
          });
          return;
      }
	  this.orderHeader.orderCode = this.orderNo;

      // Prepare the CreateOrderRequest DTO by combining the order DTO and transaction DTO
      const updatedOrderLines = this.orderLines.map(line => ({
          ...line // Copy existing properties; transNo will be handled by the backend
      }));
	  this.transactionDto = this.getSaleTransaction()
      const createOrderRequest: CreateOrderRequest = {
        orderDto: {
            orderHeader: this.orderHeader,
            orderLines: updatedOrderLines,
        },
        transactionDto: this.transactionDto,
    };
    
		this.checkIntegratedEftpos(createOrderRequest);
	}



	checkIntegratedEftpos(createOrderRequests: CreateOrderRequest): void {
		let intEftPayment = new Payment;
		let intEft = false;
		let intAmount = 0;
		for (const payment of this.transaction.payments) {
			if (payment.desc === "Integrated Eftpos") {
				intEftPayment = payment;
				intEft = true;
				intAmount = payment.amount;
				break;
			}
		}

		if (intEft) {
			const modalRef = this.modalService.open(WaitingForEftposModalComponent, {
				size: 'md',
				centered: true,
				backdrop: 'static',
				keyboard: false
			});
			console.log(this.transNo)
			// Determine which integrated EFTPOS provider to use
			switch (this.sysStatus.integratedEFTProvider) {
				case "Linkly":
					modalRef.componentInstance.tenderAmount = intAmount;
					modalRef.componentInstance.totalAmount = this.depositAmount;
					modalRef.componentInstance.store = this.store;
					modalRef.componentInstance.discountAmt = 0; // TODO: calculate discount if needed
					modalRef.componentInstance.surchargeAmt = intAmount * 0; // TODO: adjust surcharge calculation if required
					modalRef.componentInstance.taxAmt = intAmount * 0; // TODO: adjust tax calculation based on config
					modalRef.componentInstance.transNo = this.transNo;

					// Map the current cart to the format required by Linkly
					const mappedItems = mapCartToLinklyBasket(this.cart);
					modalRef.componentInstance.items = mappedItems;
					modalRef.componentInstance.transType = "Purchase";
					break;

				case "Tyro":
					// TODO: Implement Tyro integration logic here if needed.
					break;

				default:
					console.log("Integrated EFTPOS not configured");
					return;
			}

			// Handle the result from the waiting-for-EFTPOS modal.
			modalRef.result.then((result: { paymentResult: Payment, surchargePayment: Payment }) => {
				if (result) {
					console.log("EFTPOS payment result:", result);
					this.eftposService.getReceipts(this.transNo, false)
						.subscribe((receipts: GetReceiptDto[]) => {
							this.intEftReceipts = receipts;
							this.depositTransactionCompleted(createOrderRequests)
							this.printService.printEftposReceipt(receipts, true);
						});
				} else {
					const currentPaid = this.amountPaidSubject.getValue();
					const newPaid = currentPaid - intAmount;
					this.amountPaidSubject.next(newPaid);

					this.amountPaid -= intAmount;

					this.transaction.removePayment(intEftPayment);
					this.store.dispatch(transActions.resetTransactionConfirmation());
					this.store.dispatch(transActions.getTransactionNo());
					console.log("EFTPOS payment failed or was cancelled");
				}
			}).catch(error => {
				console.error("Error in waiting-for-EFTPOS modal:", error);
			});

		}
		else {
			this.depositTransactionCompleted(createOrderRequests)

		}
	}

	depositTransactionCompleted(createOrderRequest: CreateOrderRequest): void {
        // Calculate points based on the actual amount paid for the deposit
        const netPaymentAmount = financialRound(this.amountPaid);
        this.calculateAndAddPoints(netPaymentAmount); // Call points calculation

		// Dispatch the new action to submit the combined CreateOrderRequest
		this.store.dispatch(orderActions.submitOrderWithTransaction({ payload: createOrderRequest }));
		// Process sale and finalize transaction
		this.finalizeTransaction();

		// Optionally, display a confirmation
		console.log('Order and Transaction submitted:', createOrderRequest);

		this.store.select(sysSelectors.OpenCashTill)
		.pipe()
		.subscribe(limit => {
			this.alwaysOpenCashTill = limit || 'F';
		});

		if (this.alwaysOpenCashTill === 'T') {
			this.store.dispatch(receiptActions.executeBatch({
				payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
			}));
		}
		else {
			if (this.transactionDto.payments.some(payment => payment.paymentType === 'Cash')) {
				this.store.dispatch(receiptActions.executeBatch({
					payload: new ReceiptBatch().add_action(new OpenCashDrawerAction())
				}));
			}
		}

		const saleDateTime = new Date();

		// Check if transactionDto exists before accessing properties
		const logs = (this.transactionDto != null && this.transactionDto.translogs) ? this.transactionDto.translogs : [];
		const pays = (this.transactionDto != null && this.transactionDto.payments) ? this.transactionDto.payments : [];
		const transNoForReceipt = (logs.length > 0 && logs[0].transNo != null) ? logs[0].transNo.toString() : 'N/A';

		let trans: ReceiptTransactionDto = {
			logs: logs,
			pays: pays,
			saleDateTime: saleDateTime,
			transType: 11, // Deposit Transaction Type
            refs: []
		};

		Swal.fire({
			title: "Order Completed",
			text: "The Order was successfully submitted.",
			showCancelButton: true,
			cancelButtonText: "Email Receipt",
			confirmButtonText: "Print Receipt",
		}).then(async (result) => {
            // Get customer details, check for null
            const customerCode = (this.selectedCustomerClubMember != null) ? this.selectedCustomerClubMember.clientCode : undefined;
            const customerName = (this.selectedCustomerClubMember != null) ? `${this.selectedCustomerClubMember.firstname} ${this.selectedCustomerClubMember.surname}` : undefined;

			if (result.value) {
				await this.printService.printSolemateReceipt(
					"Deposit",
					trans.logs,
					trans.pays,
					trans.transType,
					trans.logs[0].transNo.toString(),
					SolemateReceiptOptions.default(),
					trans,
                    null, // change placeholder
                    [],   // refs placeholder
                    customerCode,
                    customerName,
                    null, // voucher balances placeholder
                    this.newCustomerPointsTotal, // Pass new points total
                    this.pointsEarned,        // Pass points earned
					null,
					this.orderNo
				);
				this.store.dispatch(staffActions.clearStaffLogin());

			} else if (result.dismiss === Swal.DismissReason.cancel) {
				this.openEmailModal(trans, this.pointsEarned, this.newCustomerPointsTotal).then(() => {	
					this.store.dispatch(staffActions.clearStaffLogin());
				});
			}
			else {
				this.store.dispatch(staffActions.clearStaffLogin());
			}
		});

		// Reset the order state if needed
		this.store.dispatch(orderActions.init());

		this.activeModal.close('Deposit Fully Paid');
	}

	// Added method to calculate and add points (minimal version, no optional chaining)
	calculateAndAddPoints(paymentAmount: number): void {
		const roundedPaymentAmount = financialRound(paymentAmount);

		if (this.selectedCustomerClubMember != null && roundedPaymentAmount > 0 && this.PointsPerDollar > 0) {
			const pointsToAward = Math.floor(roundedPaymentAmount / this.PointsPerDollar);
			if (pointsToAward > 0) {
				this.pointsEarned = pointsToAward;
				// Ensure clientPoints exists before adding
				const currentPoints = this.selectedCustomerClubMember.clientPoints != null ? this.selectedCustomerClubMember.clientPoints : 0;
				this.newCustomerPointsTotal = currentPoints + pointsToAward;
				const pointsUpdatePayload: PointsUpdatePayload = {
					clientCode: this.selectedCustomerClubMember.clientCode,
					pointsToAdjust: pointsToAward
				};
				this.store.dispatch(customerClubUpdateActions.updatePoints({ payload: pointsUpdatePayload }));
			} else {
				this.pointsEarned = 0;
				this.newCustomerPointsTotal = this.selectedCustomerClubMember.clientPoints;
			}
		} else {
			this.pointsEarned = 0;
			if (this.selectedCustomerClubMember != null) {
				this.newCustomerPointsTotal = this.selectedCustomerClubMember.clientPoints;
			} else {
				this.newCustomerPointsTotal = null;
			}
		}
	}

	ngOnDestroy() {
		this.destroy$.next();
		this.destroy$.complete();
	}
}
