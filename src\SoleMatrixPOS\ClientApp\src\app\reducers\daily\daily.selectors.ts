import { AppState } from '../index';
import { createSelector } from '@ngrx/store';

export const select = (state: AppState) => state.daily;
export const isRangeMode = (state: AppState) => state.daily.isRangeMode;
export const storeTodaysDaily = createSelector(select, (s) => s.currDaily);
export const isLoading = createSelector(select, (s) => s.isLoading);
export const storeSalesByStaff = createSelector(select, (s) => s.salesByStaff);
export const storeRefundsByStaff = createSelector(select, (s) => s.refundsByStaff);
export const storeSalesByHour = createSelector(select, (s) => s.salesByHour);
export const storeRefundsByHour = createSelector(select, (s) => s.refundsByHour);
export const storeDepartmentRefunds = createSelector(select, (s) => s.departmentRefunds);
export const getGrossSales = createSelector(select, (s) => s.grossAmount);
export const getItems = createSelector(select, (s) => s.totalItems);
export const getCusts = createSelector(select, (s) => s.totalCustomers);
export const getMultiSales = createSelector(select, (s) => s.multiSales);
export const getDepartmentSales = createSelector(select, (s) => s.departmentSales);
export const getSelectedDate = createSelector(select, (s) => s.selectedDate);
export const storeDailyRange = createSelector(select, (s) => s.dailyRange);
export const storeDailyRangeTotals = createSelector(select, (s) => s.dailyRangeTotals);

// Helper function to calculate total refunds from refunds by hour
function getTotalRefunds(refundsByHour: any[]): number {
    if (!refundsByHour) return 0;
    let sum = 0;
    refundsByHour.forEach(dto => {
        sum += dto.sales;
    });
    return sum;
}

// Helper function to calculate total refund items from refunds by hour
function getTotalRefundItems(refundsByHour: any[]): number {
    if (!refundsByHour) return 0;
    let sum = 0;
    refundsByHour.forEach(dto => {
        sum += dto.items;
    });
    return sum;
}

// Helper function to calculate total refund customers from refunds by hour
function getTotalRefundCustomers(refundsByHour: any[]): number {
    if (!refundsByHour) return 0;
    let sum = 0;
    refundsByHour.forEach(dto => {
        sum += dto.custs;
    });
    return sum;
}

// New selector for net gross sales (gross sales plus refunds, since refunds are negative)
export const getNetGrossSales = createSelector(
    select,
    (s) => {
        const grossSales = s.grossAmount || 0;
        const totalRefunds = getTotalRefunds(s.refundsByHour);
        return grossSales + totalRefunds; // Adding because refunds are already negative values
    }
);

// New selector for net items (items minus refund items)
export const getNetItems = createSelector(
    select,
    (s) => {
        const totalItems = s.totalItems || 0;
        const totalRefundItems = getTotalRefundItems(s.refundsByHour);
        return totalItems + totalRefundItems; // Adding because refund items are negative
    }
);

// New selector for net customers (customers minus refund customers)
export const getNetCustomers = createSelector(
    select,
    (s) => {
        const totalCustomers = s.totalCustomers || 0;
        const totalRefundCustomers = getTotalRefundCustomers(s.refundsByHour);
        return totalCustomers + totalRefundCustomers; // Adding because refund customers are negative
    }
);

// New selector for gross sales only (no refunds)
export const getGrossSalesOnly = createSelector(
    select,
    (s) => s.grossAmount || 0
);

// New selector for gross refunds only (absolute value of refunds)
export const getGrossRefundsOnly = createSelector(
    select,
    (s) => {
        const totalRefunds = getTotalRefunds(s.refundsByHour);
        return Math.abs(totalRefunds); // Return absolute value for display
    }
);

// New selector for refund items only (absolute value)
export const getRefundItemsOnly = createSelector(
    select,
    (s) => {
        const totalRefundItems = getTotalRefundItems(s.refundsByHour);
        return Math.abs(totalRefundItems); // Return absolute value for display
    }
);

// New selector for refund customers only (absolute value)
export const getRefundCustomersOnly = createSelector(
    select,
    (s) => {
        const totalRefundCustomers = getTotalRefundCustomers(s.refundsByHour);
        return Math.abs(totalRefundCustomers); // Return absolute value for display
    }
);